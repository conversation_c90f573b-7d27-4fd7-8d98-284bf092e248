<template>
    <div ref="backgroundInfoRef" class="background-info-question-wrap">
        <div class="background-info-contetn" :style="topVal ? { paddingBottom: `${topVal}px` } : {}">
            <QuestionTitle :questionInfo="info" />
        </div>

        <div ref="answerPanelRef" class="children-answer-panel" :style="topVal ? { height: `${topVal}px` } : {}">
            <div class="children-answer-panel-content">
                <div
                    class="controller-bar"
                    :class="{
                        touch: isTouch,
                    }"
                    @touchstart="onTouchstart"
                    @touchmove="onTouchmove"
                    @touchend="onTouchEnd"
                />
                <div class="slot-content-wrap">
                    <div class="slot-tit-wrap">
                        <p class="question-type-name-tag">
                            {{ questionTypeName }}
                        </p>
                    </div>
                    <slot />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { debounce, throttle } from 'lodash-es';
import { computed, onMounted, ref } from 'vue';
import { questionTypeMap } from '../../../constant';
import QuestionTitle from '../../../components/question/component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    questionBackgroundInfo: {
        type: Object,
        default: () => ({}),
    },
});

const questionTypeName = computed(() => {
    let currentQuestionType = 1;

    if (props.questionInfo && Object.keys(props.questionInfo).length) {
        currentQuestionType = props.questionInfo.questionType;
    }

    return questionTypeMap[currentQuestionType]?.shortName;
});

const info = computed(() => {
    const { backgroundInfo, backgroundFiles } = props.questionBackgroundInfo;
    return {
        questionTitle: `${backgroundInfo}`,
        files: backgroundFiles,
    };
});
const topVal = ref<number>(0);
const answerPanelRef = ref();
const isTouch = ref(false);
const backgroundInfoRef = ref();

const startPoint = {
    y: 0, // 滑动起始值
    startY: 0, // 起始距离
    minHeight: 225,
    maxHeight: 0,
};

function onTouchstart(e: TouchEvent) {
    e.stopPropagation();
    e.preventDefault();
    isTouch.value = true;
    const { clientY } = e.changedTouches[0] as any;
    const panelHeight = getAnswerPanelHeight();
    const containerHeight = backgroundInfoRef.value.offsetHeight;

    startPoint.maxHeight = containerHeight * 0.75;

    startPoint.y = clientY;
    startPoint.startY = panelHeight;
}

const onTouchmove = throttle((e: TouchEvent) => {
    e.stopPropagation();
    e.preventDefault();
    const { y, startY } = startPoint;
    const { clientY } = e.changedTouches[0] as any;
    const diffY = y - clientY; // 手指纵向移动距离
    const height = startY + diffY;
    topVal.value = height;
}, 66);

const onTouchEnd = debounce((e: TouchEvent) => {
    e.stopPropagation();
    e.preventDefault();
    isTouch.value = false;

    const { minHeight, maxHeight } = startPoint;
    const height = topVal.value;
    if (height >= maxHeight) {
        topVal.value = maxHeight;
    } else if (height <= minHeight) {
        topVal.value = minHeight;
    }
    startPoint.y = 0;
    startPoint.startY = 0;
}, 66);

function getAnswerPanelHeight() {
    const height = answerPanelRef.value?.offsetHeight;
    return height;
}

onMounted(() => {
    const height = getAnswerPanelHeight();
    topVal.value = height;
});
</script>

<style lang="less" scoped>
.background-info-question-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid transparent;
    overflow: hidden;

    .background-info-contetn {
        flex: 1;
        overflow: auto;
    }

    .children-answer-panel {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        // height: 50%;
        max-height: 75% !important;
        min-height: 225px !important;
        background: #fff;
        box-shadow: 0 -5px 12px 0 rgba(0, 0, 0, 0.12);
        border-radius: 16px 16px 0 0;
        transition: all 0.12s;

        .children-answer-panel-content {
            position: relative;
            height: 100%;
            min-height: 100%;
            padding: 20px 0;
            padding-bottom: 0;
            display: flex;

            .controller-bar {
                position: absolute;
                // margin-top: -12px;
                left: 50%;
                top: 0;
                padding: 8px 30px 20px;
                transform: translateX(-50%);

                &.touch {
                    &::after {
                        background-color: var(--van-primary-color) !important;
                    }
                }

                &::after {
                    content: '';
                    display: block;
                    height: 4px;
                    width: 40px;
                    background: #e0e0e0;
                    border-radius: 2px;
                    transition: all 0.2s;
                }
            }

            .slot-content-wrap {
                flex: 1;
                overflow: auto;

                .slot-tit-wrap {
                    display: flex;
                    padding: 0 20px 12px;
                    .question-type-name-tag {
                        padding: 0 5px;
                        background: #e4f6f6;
                        border-radius: 4px;
                        color: #00a6a7;
                        font-size: 13px;
                        font-weight: 400;
                    }
                }
            }
        }
    }
}
</style>
