<template>
    <div class="player-card-container" :style="{ '--size': `${cardSize}px` }">
        <div class="player-name-wrap" :style="{ width: `${nameBarWidth}px`, backgroundImage: `url(${nameBarBg})` }">
            <Text
                :text="playerName"
                font-family="DingTalk JinBuTi"
                font-size="14px"
                :font-weight="400"
                strokeColor="#FF8834"
                :stroke-width="1"
                textColor="#FFF9DC"
                :shadowFilterConfig="{ x: 0, y: 1, blur: 2, color: 'rgba(229, 87, 17, 0.50)' }"
            />
        </div>
        <Transition name="player-join-fade">
            <div v-if="showJoin" class="join-symbol" />
        </Transition>
        <div v-if="avatarConfig?.show" class="avatar">
            <img :src="avatarConfig?.url" alt="" />
        </div>
        <div class="coin-button" :style="{ '--button-width': `${buttonWidth}px`, '--bottom-position': `${buttonBottom}px`, backgroundImage: `url(${buttonBg})` }">
            <Text text="金币" v-bind="coinButtonStyleAttr" />
            <Text needAnimation :text="coin" v-bind="coinButtonStyleAttr" />
        </div>
        <div v-if="castShadow" class="card-shadow" :style="{ '--shadow-bottom-position': `${shadowBottomPosition}px` }" />
        <div v-show="fundAnimationConfig?.show" ref="playerFundRef" class="player-fund-change-wrap">
            <Text
                :key="fundAnimationConfig.text"
                :text="fundAnimationConfig.text"
                font-family="PingFang SC"
                font-size="24px"
                :font-weight="600"
                :stroke-width="1"
                :strokeColor="fundTextAttrSet['stroke-color']"
                :textColor="fundTextAttrSet['text-color']"
                :shadowFilterConfig="{ x: 0, y: 1, blur: 2, color: fundTextAttrSet['shadow-color']! }"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { gsap, Power3 } from 'gsap';
import { computed, onMounted, ref, watch } from 'vue';
import Text from '../../../component/text.vue';

defineOptions({
    name: 'PlayerCard',
});
const props = withDefaults(defineProps<IProps>(), {
    playerName: '',
    coin: 0,
    size: 'small',
    castShadow: false,
    showJoin: false,
    avatarConfig: () => ({ show: false, url: '' }),
    fundAnimationConfig: () => ({ show: false, text: '', action: '+' }),
});
const emits = defineEmits(['fundStartVanish', 'fundAnimationEnd']);
interface IProps {
    playerName?: string;
    coin?: number | string;
    size?: 'large' | 'small';
    /**
     * 是否渲染底部阴影
     */
    castShadow?: boolean;
    /**
     * 是否显示”已参与“
     */
    showJoin?: boolean;
    /**
     * 人物剪影配置
     */
    avatarConfig?: { show: boolean; url: string };
    /**
     * 人物底部资金配置
     */
    fundAnimationConfig?: { show: boolean; text: string; action: '+' | '-' };
}
const cardSize = props.size === 'large' ? 152 : 120;
const nameBarWidth = props.size === 'large' ? 108 : 89;
const nameBarBg =
    props.size === 'large' ? 'https://img.bosszhipin.com/static/file/2024/kshj5xwm3m1726709168272.svg' : 'https://img.bosszhipin.com/static/file/2024/6o3nzbgex51726709167980.svg';
const buttonWidth = props.size === 'large' ? 142 : 122;
const buttonBg =
    props.size === 'large' ? 'https://img.bosszhipin.com/static/file/2024/mx3afy75hn1726713636137.svg' : 'https://img.bosszhipin.com/static/file/2024/1zbpxo4xzl1726712540111.svg';
const buttonBottom = props.size === 'large' ? 0 : -7;
const shadowBottomPosition = props.size === 'large' ? 8 : 10;
const coinButtonStyleAttr = {
    'font-family': 'PingFang SC',
    'font-size': '20px',
    'font-weight': 600,
    'stroke-color': '#DB6400',
    'stroke-width': 1,
    'text-color': '#FFF9DC',
    'shadow-filter-config': { x: 0, y: 0.87, blur: 0.87, color: 'rgba(218, 79, 12, 0.70)' },
};
const fundTextAttrSet = computed(() => {
    const attrSet: { 'stroke-color'?: string; 'text-color'?: string; 'shadow-color'?: string } = {};
    if (props.fundAnimationConfig.action === '+') {
        attrSet['stroke-color'] = 'rgba(219, 100, 0, 1)';
        attrSet['text-color'] = 'rgba(255, 251, 232, 1)';
        attrSet['shadow-color'] = 'rgba(218, 79, 12, 0.70)';
    } else if (props.fundAnimationConfig.action === '-') {
        attrSet['stroke-color'] = 'rgba(255, 255, 255, 1)';
        attrSet['text-color'] = 'rgba(232, 81, 81, 1)';
        attrSet['shadow-color'] = 'rgba(255, 215, 215, 0.70)';
    }
    return attrSet;
});

const playerFundRef = ref();
function registryFundAnimation(): gsap.core.Timeline {
    const t1 = gsap.timeline({
        paused: true,
        onComplete: () => {
            emits('fundAnimationEnd');
        },
    });
    t1.set(playerFundRef.value, { opacity: 0, y: 10 }).call(() => {}); // 如果需要回调，在每一步都可以插入
    t1.to(playerFundRef.value, { duration: 0.3, opacity: 1, y: 0, ease: Power3.easeOut }, '>');
    t1.to(playerFundRef.value, { duration: 0.3, opacity: 0, y: -10, ease: Power3.easeIn }, '>0.6').call(
        () => {
            emits('fundStartVanish');
        },
        [],
        '<',
    );
    return t1;
}
onMounted(() => {
    watch(
        () => props.fundAnimationConfig.show,
        async (show) => {
            if (show) {
                const t1 = registryFundAnimation();
                t1.play(0);
            }
        },
        { immediate: true },
    );
});
</script>

<style lang="less" scoped>
.player-join-fade-enter-active,
.player-join-fade-leave-active {
    transition: all 0.3s linear;
}
.player-join-fade-enter-from,
.player-join-fade-leave-to {
    opacity: 0;
    transform: translateY(-50%) scale(0.5) !important;
}
.player-join-fade-enter-to,
.player-join-fade-leave-from {
    opacity: 1;
    transform: translateY(-50%) scale(1) !important;
}
</style>

<style lang="less" scoped>
.player-card-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: var(--size);
    height: var(--size);
    background: linear-gradient(179.99998deg, #ffba38 0%, rgba(255, 255, 255) 100%);
    border-radius: 20px;
    border: 5px solid #fffee0;

    .player-name-wrap {
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .join-symbol {
        background-image: url(https://img.bosszhipin.com/static/file/2024/zemfaw5sil1726725553432.png.webp);
        background-size: contain;
        background-repeat: no-repeat;
        width: 43px;
        height: 44px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }

    .avatar {
        width: 86px;
        height: 95px;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        img {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .coin-button {
        width: var(--button-width);
        height: 39.5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;
        position: absolute;
        bottom: var(--bottom-position);
    }

    .card-shadow {
        background-image: url(https://img.bosszhipin.com/static/file/2024/huel1hwtew1726712883455.svg);
        width: 120px;
        height: 18px;
        opacity: 0.5;
        position: absolute;
        bottom: 0;
        transform: translateY(calc(100% + 5px + var(--shadow-bottom-position)));
    }

    .player-fund-change-wrap {
        position: absolute;
        top: calc(100% + 39px);
        line-height: 34px;
    }
}
</style>
